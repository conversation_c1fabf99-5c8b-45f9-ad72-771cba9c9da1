import { showError } from '../utils/common'

/**
 * 台风API服务类
 */
class TyphoonApiService {
  /**
   * 获取台风列表
   * @returns {Promise<Object>} API响应数据
   */
  async getStormList() {
    try {
      const url = '/hwm/typhoon/getTyphoonList'
      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // 新接口直接返回数组，包装成原有格式
      return {
        storm: data,
        updateTime: new Date().toISOString()
      }
    } catch (error) {
      showError(`获取台风列表失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 获取台风预测数据
   * @param {string} stormId - 台风ID (tfid)
   * @returns {Promise<Object>} API响应数据
   */
  async getStormForecast(stormId) {
    try {
      const url = `/hwm/typhoon/getTyphoonDetail?tfid=${stormId}`
      const response = await fetch(url)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // 转换新接口数据格式为原有格式
      return {
        forecast: this.transformDetailToForecast(data),
        updateTime: new Date().toISOString()
      }
    } catch (error) {
      showError(`获取台风详情失败: ${error.message}`)
      throw error
    }
  }

  /**
   * 转换台风详情数据为预测格式
   * @param {Object} detailData - 台风详情数据
   * @returns {Array} 预测数据数组
   */
  transformDetailToForecast(detailData) {
    if (!detailData || !detailData.points || !Array.isArray(detailData.points)) {
      return []
    }

    const forecast = []

    // 处理历史路径点
    detailData.points.forEach(point => {
      forecast.push({
        lat: parseFloat(point.lat),
        lon: parseFloat(point.lng),
        pressure: point.pressure,
        windSpeed: point.speed,
        type: point.strong,
        fxTime: point.time,
        isHistorical: true
      })

      // 处理预测数据
      if (point.forecast && Array.isArray(point.forecast)) {
        point.forecast.forEach(forecastGroup => {
          if (forecastGroup.forecastpoints && Array.isArray(forecastGroup.forecastpoints)) {
            forecastGroup.forecastpoints.forEach(fp => {
              forecast.push({
                lat: parseFloat(fp.lat),
                lon: parseFloat(fp.lng),
                pressure: fp.pressure,
                windSpeed: fp.speed,
                type: fp.strong,
                fxTime: fp.time,
                isHistorical: false,
                forecastSource: forecastGroup.tm
              })
            })
          }
        })
      }
    })

    return forecast
  }

  /**
   * 过滤活跃台风
   * @param {Array} storms - 台风列表
   * @returns {Array} 活跃台风列表
   */
  filterActiveStorms(storms) {
    // 新接口数据结构调整，使用tfid作为id，所有返回的台风都视为活跃
    return storms.map(storm => ({
      id: storm.tfid,
      name: storm.name,
      enname: storm.enname,
      isActive: "1", // 所有台风都标记为活跃
      lat: storm.lat,
      lng: storm.lng,
      strong: storm.strong,
      power: storm.power,
      speed: storm.speed,
      pressure: storm.pressure,
      time: storm.time,
      timeformate: storm.timeformate
    }))
  }

  /**
   * 验证台风数据
   * @param {Object} storm - 台风数据
   * @returns {boolean} 是否有效
   */
  validateStormData(storm) {
    return storm && storm.tfid && storm.name
  }

  /**
   * 验证预测数据
   * @param {Array} forecast - 预测数据
   * @returns {boolean} 是否有效
   */
  validateForecastData(forecast) {
    return Array.isArray(forecast) && forecast.length > 0
  }
}

// 创建单例实例
export const typhoonApi = new TyphoonApiService()

// 导出类以便测试
export { TyphoonApiService }
