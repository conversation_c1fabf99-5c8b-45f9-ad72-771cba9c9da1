<template>
  <div class="bg-dark text-white h-full overflow-x-hidden">
    <!-- 顶部导航栏 -->
    <header class="bg-dark-border backdrop-blur-md border-b border-dark-border sticky top-0 z-50">
      <div class="container mx-auto px-4 py-2">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-3">
          <div class="flex items-center space-x-2 w-full md:w-auto">
            <i class="fa fa-tint text-primary text-2xl"></i>
            <h1 class="text-2xl md:text-3xl font-bold text-gradient">台风专题监控系统</h1>
          </div>



          <div class="flex items-center space-x-4 w-full md:w-auto justify-end">
            <div class="hidden md:flex items-center space-x-2">
              <i class="fa fa-clock-o text-secondary"></i>
              <span class="text-base md:text-lg">{{ currentTime }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <i class="fa fa-refresh text-secondary"></i>
              <span class="text-base md:text-lg">上次更新: {{ updateTime }}</span>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- 主内容区 - 单列布局 -->
    <main class="container mx-auto px-4 py-6">
      <div class="grid grid-cols-1 xl:grid-cols-4 gap-6">

        <!-- 左侧：台风列表、台风详情、台风路径图 -->
        <div class="xl:col-span-1 space-y-4 overflow-y-auto">
          <!-- 台风列表 -->
          <TyphoonList
            :storm-list="stormList"
            :selected-storm-id="selectedStormId"
            @select-storm="selectStorm"
          />

          <!-- 台风详情 -->
          <TyphoonDetails
            :selected-storm-id="selectedStormId"
            :selected-storm-name="selectedStormName"
            :current-location="currentLocation"
            :typhoon-type="typhoonType"
            :current-pressure="currentPressure"
            :current-wind-speed="currentWindSpeed"
            :forecast12h="forecast12h"
            :forecast24h="forecast24h"
            :forecast48h="forecast48h"
            @refresh-storm-data="refreshStormData"
          />

          <!-- 台风路径图 -->
          <TyphoonPathChart
            :path-chart-option="pathChartOption"
            :chart-loading="chartLoading"
          />
        </div>

        <!-- 右侧：设备监控 -->
        <div class="xl:col-span-3 space-y-4">
          <DeviceMonitoring
            :device-data="deviceData"
            :temperature-chart-option="temperatureChartOption"
            :humidity-chart-option="humidityChartOption"
            :wind-speed-chart-option="windSpeedChartOption"
            :pressure-chart-option="pressureChartOption"
            :rainfall-chart-option="rainfallChartOption"
            :wave-chart-option="waveChartOption"
            :visibility-chart-option="visibilityChartOption"
          />
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import TyphoonList from './components/TyphoonList.vue'
import TyphoonDetails from './components/TyphoonDetails.vue'
import TyphoonPathChart from './components/TyphoonPathChart.vue'
import DeviceMonitoring from './components/DeviceMonitoring.vue'

// 导入配置和工具函数
import { TYPHOON_TYPE_MAPPING, WIND_DIRECTIONS } from './config/constants'
import { createChartOption, createTyphoonPathChart } from './utils/chartUtils'
import { formatDateTime, getCurrentTime, showError, randomNumber } from './utils/common'
import { typhoonApi } from './services/typhoonApi'
import { TyphoonDataProcessor, DeviceDataProcessor } from './utils/dataProcessor'

// 响应式数据
const currentTime = ref('加载中...')
const updateTime = ref('加载中...')
const stormList = ref([])
const selectedStormId = ref(null)
const selectedStormName = ref('台风名称')
const chartLoading = ref(false)

// 台风详情数据
const currentLocation = ref('--')
const typhoonType = ref('--')
const currentPressure = ref('-- hPa')
const currentWindSpeed = ref('-- m/s')

// 预测数据
const forecast12h = reactive({ location: '--', wind: '--' })
const forecast24h = reactive({ location: '--', wind: '--' })
const forecast48h = reactive({ location: '--', wind: '--' })

// 设备数据
const deviceData = reactive({
  temperature: '26.5',
  humidity: '78',
  windSpeed: '18.2',
  windDirection: 45,
  windDirectionText: '东北偏北',
  pressure: '1002',
  rainfall: '25.8',
  waveHeight: '3.2',
  visibility: '8.5'
})

// 台风路径图表配置
const pathChartOption = ref(null)

// 设备图表配置
const temperatureChartOption = computed(() => createChartOption('temperature'))
const humidityChartOption = computed(() => createChartOption('humidity'))
const windSpeedChartOption = computed(() => createChartOption('windSpeed'))
const pressureChartOption = computed(() => createChartOption('pressure'))
const rainfallChartOption = computed(() => createChartOption('rainfall'))
const waveChartOption = computed(() => createChartOption('wave'))
const visibilityChartOption = computed(() => createChartOption('visibility'))

// 方法
const updateCurrentTime = () => {
  currentTime.value = getCurrentTime()
}

// 获取台风列表
const fetchStormList = async () => {
  try {
    const data = await typhoonApi.getStormList()
    updateTime.value = formatDateTime(data.updateTime)

    // 过滤活跃台风
    const activeStorms = typhoonApi.filterActiveStorms(data.storm)
    stormList.value = activeStorms

    // 如果有活跃台风，默认选中第一个
    if (activeStorms.length > 0) {
      selectStorm(activeStorms[0].id)
    }
  } catch (error) {
    // 错误已在API服务中处理
    console.error('获取台风列表失败:', error)
  }
}

// 获取台风预测数据
const fetchStormForecast = async (stormId) => {
  try {
    chartLoading.value = true
    const data = await typhoonApi.getStormForecast(stormId)

    updateTime.value = formatDateTime(data.updateTime)

    // 渲染台风详情
    renderStormDetails(data.forecast)

    // 渲染台风路径图
    renderTyphoonPathChart(data.forecast)
  } catch (error) {
    // 错误已在API服务中处理
    console.error('获取台风详情失败:', error)
  } finally {
    chartLoading.value = false
  }
}

// 选择台风
const selectStorm = (stormId) => {
  if (selectedStormId.value === stormId) return

  selectedStormId.value = stormId

  // 获取台风详情
  const selectedStorm = stormList.value.find(storm => storm.id === stormId)
  if (selectedStorm) {
    selectedStormName.value = selectedStorm.name
    fetchStormForecast(stormId)
  } else {
    showError('未找到选中的台风信息')
  }
}

// 渲染台风详情
const renderStormDetails = (forecast) => {
  try {
    const processedData = TyphoonDataProcessor.processStormDetails(forecast)

    // 更新当前数据
    currentLocation.value = processedData.current.location
    currentPressure.value = processedData.current.pressure
    currentWindSpeed.value = processedData.current.windSpeed
    typhoonType.value = processedData.current.type

    // 更新预测数据
    forecast12h.location = processedData.forecast12h.location
    forecast12h.wind = processedData.forecast12h.wind
    forecast24h.location = processedData.forecast24h.location
    forecast24h.wind = processedData.forecast24h.wind
    forecast48h.location = processedData.forecast48h.location
    forecast48h.wind = processedData.forecast48h.wind
  } catch (error) {
    showError(`渲染台风详情时出错: ${error.message}`)
    console.error('渲染台风详情错误:', error)
  }
}

// 渲染台风路径图
const renderTyphoonPathChart = (forecast) => {
  try {
    pathChartOption.value = createTyphoonPathChart(forecast, TYPHOON_TYPE_MAPPING)
  } catch (error) {
    showError(`渲染台风路径图时出错: ${error.message}`)
    console.error('渲染台风路径图错误:', error)
  }
}

// 刷新台风数据
const refreshStormData = () => {
  if (selectedStormId.value) {
    fetchStormForecast(selectedStormId.value)
  } else {
    fetchStormList()
  }
}

// 模拟设备数据更新
const simulateDeviceData = () => {
  // 随机风向 (0-360度)
  const windDirection = randomNumber(0, 360)
  deviceData.windDirection = windDirection

  // 更新风向文本
  deviceData.windDirectionText = DeviceDataProcessor.getWindDirectionText(windDirection, WIND_DIRECTIONS)

  // 随机更新其他设备数据
  deviceData.temperature = DeviceDataProcessor.formatDeviceValue(randomNumber(25, 28, 1))
  deviceData.humidity = randomNumber(70, 85).toString()
  deviceData.windSpeed = DeviceDataProcessor.formatDeviceValue(randomNumber(15, 23, 1))
  deviceData.pressure = DeviceDataProcessor.formatDeviceValue(randomNumber(1000, 1005, 1))
  deviceData.rainfall = DeviceDataProcessor.formatDeviceValue(randomNumber(20, 35, 1))
  deviceData.waveHeight = DeviceDataProcessor.formatDeviceValue(randomNumber(2, 5, 1))
  deviceData.visibility = DeviceDataProcessor.formatDeviceValue(randomNumber(5, 13, 1))
}

// 定时器
let timeInterval = null
let deviceInterval = null

// 生命周期
onMounted(() => {
  // 更新当前时间
  updateCurrentTime()
  timeInterval = setInterval(updateCurrentTime, 1000)

  // 获取台风列表
  fetchStormList()

  // 模拟设备数据更新
  simulateDeviceData()
  deviceInterval = setInterval(simulateDeviceData, 5000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  if (deviceInterval) {
    clearInterval(deviceInterval)
  }
})
</script>

<style scoped>
@import './styles/common.css';
</style>